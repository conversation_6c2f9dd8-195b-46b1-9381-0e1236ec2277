<template>
    <div class="invoice-info">
        <div class="invoice-header">
            <h3>发票信息</h3>
            <p class="invoice-desc">查看该订单的发票申请记录和状态</p>
        </div>

        <div class="invoice-content" v-loading="loading">
            <!-- 发票申请记录表格 -->
            <el-table :data="invoiceList" stripe style="width: 100%" empty-text="暂无发票申请记录">
                <el-table-column prop="id" label="申请ID" width="100" />
                <el-table-column prop="orderNo" label="订单号" width="200" />
                <el-table-column prop="invoiceType" label="发票类型" width="150">
                    <template #default="{ row }">
                        <span>{{ getInvoiceTypeLabel(row.invoiceType) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="billMoney" label="开票金额" width="120">
                    <template #default="{ row }">
                        <span class="amount">¥{{ formatMoney(row.billMoney) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="invoiceStatus" label="申请状态" width="120">
                    <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.invoiceStatus)">
                            {{ getStatusLabel(row.invoiceStatus) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="applicationTime" label="申请时间" width="180">
                    <template #default="{ row }">
                        <span>{{ formatDateTime(row.applicationTime) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="billingRemarks" label="开票备注" min-width="150">
                    <template #default="{ row }">
                        <span>{{ row.billingRemarks || '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                    <template #default="{ row }">
                        <el-button type="primary" link size="small" @click="handleViewDetail(row)"> 查看详情 </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper" v-if="total > 0">
                <el-pagination
                    v-model:current-page="pageConfig.current"
                    v-model:page-size="pageConfig.size"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>

            <!-- 空状态 -->
            <div v-if="!loading && invoiceList.length === 0" class="empty-state">
                <el-empty description="暂无发票申请记录" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts" name="InvoiceInfo">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { doGetinvoiceRequestList } from '@/apis/invoice'
import type { ApiOrder } from '@/views/order/types/order'

interface Props {
    order?: ApiOrder
}

const props = defineProps<Props>()
const $route = useRoute()
const $router = useRouter()

// 响应式数据
const loading = ref(false)
const invoiceList = ref<any[]>([])
const total = ref(0)

// 分页配置
const pageConfig = reactive({
    current: 1,
    size: 10,
})

// 获取订单号
const orderNo = computed(() => {
    return props.order?.no || ($route.query.orderNo as string) || ''
})

/**
 * 加载发票申请记录
 */
const loadInvoiceList = async () => {
    if (!orderNo.value) {
        ElMessage.warning('订单号不能为空')
        return
    }

    try {
        loading.value = true
        console.log('查询发票申请记录，订单号:', orderNo.value)

        const params = {
            orderNo: orderNo.value,
            applyType: 'OWNER', // 申请类型
            invoiceOwnerType: 'SUPPLIER', // 发票所属方类型（供应商端）
            current: pageConfig.current,
            size: pageConfig.size,
        }

        console.log('发票申请查询参数:', params)
        console.log('接口地址: http://192.168.1.254:9999/addon-invoice/invoice/invoiceRequest')

        const { code, data, msg } = await doGetinvoiceRequestList(params)

        if (code === 200 && data) {
            console.log('发票申请记录查询成功:', data)
            invoiceList.value = data.records || data || []
            total.value = data.total || 0
        } else {
            console.error('发票申请记录查询失败:', msg)
            ElMessage.error(msg || '查询发票申请记录失败')
            invoiceList.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('查询发票申请记录异常:', error)
        ElMessage.error('查询发票申请记录失败，请稍后重试')
        invoiceList.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

/**
 * 格式化金额显示
 */
const formatMoney = (money: string | number) => {
    if (!money) return '0.00'
    const amount = typeof money === 'string' ? parseFloat(money) : money
    return (amount / 100).toFixed(2) // 假设后端返回的是分为单位
}

/**
 * 获取发票类型标签
 */
const getInvoiceTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
        VAT_GENERAL: '增值税普通发票',
        VAT_SPECIAL: '增值税专用发票',
    }
    return labels[type] || type
}

/**
 * 获取状态标签
 */
const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
        REQUEST_IN_PROCESS: '申请中',
        SUCCESSFULLY_INVOICED: '开票成功',
        FAILED_INVOICE_REQUEST: '申请失败',
    }
    return labels[status] || status
}

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: string) => {
    const types: Record<string, string> = {
        REQUEST_IN_PROCESS: 'warning',
        SUCCESSFULLY_INVOICED: 'success',
        FAILED_INVOICE_REQUEST: 'danger',
    }
    return types[status] || 'info'
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
}

/**
 * 查看详情
 */
const handleViewDetail = (row: any) => {
    console.log('查看发票详情:', row)
    // 根据记忆中的需求，应该跳转到发票详情页面
    // 使用发票详情API: http://192.168.1.254:9999/addon-invoice/invoice/invoiceRequest/{id}
    $router.push({
        path: '/finance/invoicing-request',
        query: {
            detailId: row.id, // 发票申请ID
        },
    })
}

/**
 * 分页大小改变
 */
const handleSizeChange = (size: number) => {
    pageConfig.size = size
    pageConfig.current = 1
    loadInvoiceList()
}

/**
 * 当前页改变
 */
const handleCurrentChange = (current: number) => {
    pageConfig.current = current
    loadInvoiceList()
}

// 组件挂载时加载数据
onMounted(() => {
    loadInvoiceList()
})
</script>

<style scoped lang="scss">
.invoice-info {
    padding: 20px;
    background: #fff;
    border-radius: 8px;

    .invoice-header {
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid #eee;

        h3 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 18px;
            font-weight: 600;
        }

        .invoice-desc {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
    }

    .invoice-content {
        .amount {
            color: #f56c6c;
            font-weight: 600;
        }

        .pagination-wrapper {
            margin-top: 20px;
            display: flex;
            justify-content: center;
        }

        .empty-state {
            margin-top: 40px;
        }
    }
}
</style>
